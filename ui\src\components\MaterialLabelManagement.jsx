import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Business as BusinessIcon,
  MusicNote as MusicIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { adminService } from '../services/api';

const MaterialLabelManagement = () => {
  const [labels, setLabels] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedLabel, setSelectedLabel] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Form state for creating new label
  const [newLabel, setNewLabel] = useState({
    // User account details
    username: '',
    email: '',
    password: '',
    // Label details
    labelName: '',
    displayName: '',
    companyType: 'independent',
    foundedYear: '',
    phone: '',
    website: '',
    description: '',
    genres: [],
    address: {
      street: '',
      city: '',
      state: '',
      country: 'ZA',
      postalCode: ''
    },
    businessRegistration: {
      registrationNumber: '',
      taxNumber: '',
      vatNumber: ''
    }
  });

  useEffect(() => {
    loadLabels();
  }, [page, searchTerm, statusFilter]);

  const loadLabels = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: 20,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter })
      };
      
      const response = await adminService.getAllLabels(params);
      setLabels(response.data?.data || []);
      setTotalPages(response.data?.pagination?.pages || 1);
    } catch (error) {
      console.error('Failed to load labels:', error);
      setLabels([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateLabel = async () => {
    try {
      const labelData = {
        username: newLabel.username,
        email: newLabel.email,
        password: newLabel.password,
        role: 'label',
        labelData: {
          labelName: newLabel.labelName,
          displayName: newLabel.displayName,
          companyType: newLabel.companyType,
          foundedYear: newLabel.foundedYear ? parseInt(newLabel.foundedYear) : undefined,
          phone: newLabel.phone,
          website: newLabel.website,
          description: newLabel.description,
          genres: Array.isArray(newLabel.genres) ? newLabel.genres : [newLabel.genres].filter(Boolean),
          address: newLabel.address,
          businessRegistration: newLabel.businessRegistration
        }
      };

      await adminService.createUser(labelData);
      setCreateDialogOpen(false);
      setNewLabel({
        username: '',
        email: '',
        password: '',
        labelName: '',
        displayName: '',
        companyType: 'independent',
        foundedYear: '',
        phone: '',
        website: '',
        description: '',
        genres: [],
        address: { street: '', city: '', state: '', country: 'ZA', postalCode: '' },
        businessRegistration: { registrationNumber: '', taxNumber: '', vatNumber: '' }
      });
      loadLabels();
    } catch (error) {
      console.error('Failed to create label:', error);
    }
  };

  const handleViewLabel = async (labelId) => {
    try {
      const response = await adminService.getLabelById(labelId);
      setSelectedLabel(response.data?.data);
      setDialogOpen(true);
    } catch (error) {
      console.error('Failed to load label details:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'suspended': return 'error';
      default: return 'default';
    }
  };

  const formatGenres = (genres) => {
    if (!Array.isArray(genres)) return 'Unknown';
    return genres.slice(0, 2).join(', ') + (genres.length > 2 ? '...' : '');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          Label Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Label Account
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search labels..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPage(1);
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Labels Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Labels ({labels.length})
          </Typography>
          
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Label</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Type</TableCell>
                  <TableCell>Genres</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {labels.map((label) => (
                  <TableRow key={label._id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: '#1976d2' }}>
                          <BusinessIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {label.labelName}
                          </Typography>
                          {label.displayName && label.displayName !== label.labelName && (
                            <Typography variant="caption" color="text.secondary">
                              {label.displayName}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{label.email}</Typography>
                        {label.phone && (
                          <Typography variant="caption" color="text.secondary">
                            {label.phone}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={label.companyType || 'Independent'}
                        size="small"
                        variant="outlined"
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatGenres(label.genres)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={label.accountStatus || 'Unknown'}
                        color={getStatusColor(label.accountStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewLabel(label._id)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {labels.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <BusinessIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No labels found
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Create Label Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Label Account</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* User Account Section */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Account Details</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Username"
                value={newLabel.username}
                onChange={(e) => setNewLabel({ ...newLabel, username: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newLabel.email}
                onChange={(e) => setNewLabel({ ...newLabel, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newLabel.password}
                onChange={(e) => setNewLabel({ ...newLabel, password: e.target.value })}
                required
              />
            </Grid>

            {/* Label Details Section */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Label Information</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Label Name"
                value={newLabel.labelName}
                onChange={(e) => setNewLabel({ ...newLabel, labelName: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Display Name"
                value={newLabel.displayName}
                onChange={(e) => setNewLabel({ ...newLabel, displayName: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>Company Type</InputLabel>
                <Select
                  value={newLabel.companyType}
                  onChange={(e) => setNewLabel({ ...newLabel, companyType: e.target.value })}
                  label="Company Type"
                >
                  <MenuItem value="independent">Independent</MenuItem>
                  <MenuItem value="major">Major Label</MenuItem>
                  <MenuItem value="subsidiary">Subsidiary</MenuItem>
                  <MenuItem value="distributor">Distributor</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Founded Year"
                type="number"
                value={newLabel.foundedYear}
                onChange={(e) => setNewLabel({ ...newLabel, foundedYear: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                value={newLabel.phone}
                onChange={(e) => setNewLabel({ ...newLabel, phone: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website"
                value={newLabel.website}
                onChange={(e) => setNewLabel({ ...newLabel, website: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={newLabel.description}
                onChange={(e) => setNewLabel({ ...newLabel, description: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateLabel} variant="contained">Create Label</Button>
        </DialogActions>
      </Dialog>

      {/* View Label Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Label Details</DialogTitle>
        <DialogContent>
          {selectedLabel && (
            <Box sx={{ mt: 2 }}>
              <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                <Tab label="Profile" />
                <Tab label="Business" />
                <Tab label="Contact" />
              </Tabs>

              {tabValue === 0 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Label Name</Typography>
                      <Typography variant="body1">{selectedLabel.labelName}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Display Name</Typography>
                      <Typography variant="body1">{selectedLabel.displayName || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Description</Typography>
                      <Typography variant="body1">{selectedLabel.description || 'No description available'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Company Type</Typography>
                      <Typography variant="body1">{selectedLabel.companyType || 'Independent'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Founded Year</Typography>
                      <Typography variant="body1">{selectedLabel.foundedYear || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Genres</Typography>
                      <Typography variant="body1">{selectedLabel.genres?.join(', ') || 'N/A'}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {tabValue === 1 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Registration Number</Typography>
                      <Typography variant="body1">{selectedLabel.businessRegistration?.registrationNumber || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Tax Number</Typography>
                      <Typography variant="body1">{selectedLabel.businessRegistration?.taxNumber || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">VAT Number</Typography>
                      <Typography variant="body1">{selectedLabel.businessRegistration?.vatNumber || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Address</Typography>
                      <Typography variant="body1">
                        {selectedLabel.address ?
                          `${selectedLabel.address.street}, ${selectedLabel.address.city}, ${selectedLabel.address.state} ${selectedLabel.address.postalCode}, ${selectedLabel.address.country}`
                          : 'N/A'
                        }
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {tabValue === 2 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                      <Typography variant="body1">{selectedLabel.email}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
                      <Typography variant="body1">{selectedLabel.phone || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Website</Typography>
                      <Typography variant="body1">{selectedLabel.website || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Account Status</Typography>
                      <Chip
                        label={selectedLabel.accountStatus}
                        color={getStatusColor(selectedLabel.accountStatus)}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialLabelManagement;
