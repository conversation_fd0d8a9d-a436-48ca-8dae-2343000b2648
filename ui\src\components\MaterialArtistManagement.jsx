import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Ty<PERSON><PERSON>,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Tooltip,
  Avatar,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  MusicNote as MusicIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  Verified as VerifiedIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { adminService } from '../services/api';

const MaterialArtistManagement = () => {
  const [artists, setArtists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedArtist, setSelectedArtist] = useState(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Form state for creating new artist
  const [newArtist, setNewArtist] = useState({
    // User account details
    username: '',
    email: '',
    password: '',
    // Artist details
    artistName: '',
    stageName: '',
    realName: '',
    phone: '',
    bio: '',
    genre: [],
    country: 'ZA',
    city: '',
    website: '',
    socialMedia: {
      instagram: '',
      twitter: '',
      facebook: '',
      youtube: ''
    },
    ipiNumber: '',
    samroMemberNumber: '',
    sampraMemberNumber: ''
  });

  useEffect(() => {
    loadArtists();
  }, [page, searchTerm, statusFilter]);

  const loadArtists = async () => {
    setLoading(true);
    try {
      const params = {
        page,
        limit: 20,
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter && { status: statusFilter })
      };
      
      const response = await adminService.getAllArtists(params);
      setArtists(response.data?.data || []);
      setTotalPages(response.data?.pagination?.pages || 1);
    } catch (error) {
      console.error('Failed to load artists:', error);
      setArtists([]);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateArtist = async () => {
    try {
      const artistData = {
        username: newArtist.username,
        email: newArtist.email,
        password: newArtist.password,
        role: 'artist',
        artistData: {
          artistName: newArtist.artistName,
          stageName: newArtist.stageName,
          realName: newArtist.realName,
          phone: newArtist.phone,
          bio: newArtist.bio,
          genre: Array.isArray(newArtist.genre) ? newArtist.genre : [newArtist.genre].filter(Boolean),
          country: newArtist.country,
          city: newArtist.city,
          website: newArtist.website,
          socialMedia: newArtist.socialMedia,
          ipiNumber: newArtist.ipiNumber,
          samroMemberNumber: newArtist.samroMemberNumber,
          sampraMemberNumber: newArtist.sampraMemberNumber
        }
      };

      await adminService.createUser(artistData);
      setCreateDialogOpen(false);
      setNewArtist({
        username: '',
        email: '',
        password: '',
        artistName: '',
        stageName: '',
        realName: '',
        phone: '',
        bio: '',
        genre: [],
        country: 'ZA',
        city: '',
        website: '',
        socialMedia: { instagram: '', twitter: '', facebook: '', youtube: '' },
        ipiNumber: '',
        samroMemberNumber: '',
        sampraMemberNumber: ''
      });
      loadArtists();
    } catch (error) {
      console.error('Failed to create artist:', error);
    }
  };

  const handleViewArtist = async (artistId) => {
    try {
      const response = await adminService.getArtistById(artistId);
      setSelectedArtist(response.data?.data);
      setDialogOpen(true);
    } catch (error) {
      console.error('Failed to load artist details:', error);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'active': return 'success';
      case 'pending': return 'warning';
      case 'suspended': return 'error';
      default: return 'default';
    }
  };

  const formatGenres = (genres) => {
    if (!Array.isArray(genres)) return 'Unknown';
    return genres.slice(0, 2).join(', ') + (genres.length > 2 ? '...' : '');
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Typography variant="h4" gutterBottom sx={{ fontWeight: 'bold', color: '#1976d2' }}>
          Artist Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setCreateDialogOpen(true)}
        >
          Create Artist Account
        </Button>
      </Box>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                placeholder="Search artists..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All Status</MenuItem>
                  <MenuItem value="active">Active</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="suspended">Suspended</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <Button
                fullWidth
                variant="outlined"
                onClick={() => {
                  setSearchTerm('');
                  setStatusFilter('');
                  setPage(1);
                }}
              >
                Clear Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Artists Table */}
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Artists ({artists.length})
          </Typography>
          
          <TableContainer component={Paper} sx={{ mt: 2 }}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Artist</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Genres</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Verification</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {artists.map((artist) => (
                  <TableRow key={artist._id}>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Avatar sx={{ bgcolor: '#1976d2' }}>
                          <PersonIcon />
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="medium">
                            {artist.artistName}
                          </Typography>
                          {artist.stageName && (
                            <Typography variant="caption" color="text.secondary">
                              Stage: {artist.stageName}
                            </Typography>
                          )}
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2">{artist.email}</Typography>
                        {artist.phone && (
                          <Typography variant="caption" color="text.secondary">
                            {artist.phone}
                          </Typography>
                        )}
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatGenres(artist.genre)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={artist.accountStatus || 'Unknown'}
                        color={getStatusColor(artist.accountStatus)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {artist.verification?.isVerified ? (
                        <Chip
                          icon={<VerifiedIcon />}
                          label="Verified"
                          color="success"
                          size="small"
                        />
                      ) : (
                        <Chip
                          icon={<WarningIcon />}
                          label="Unverified"
                          color="warning"
                          size="small"
                        />
                      )}
                    </TableCell>
                    <TableCell>
                      <Tooltip title="View Details">
                        <IconButton
                          size="small"
                          onClick={() => handleViewArtist(artist._id)}
                        >
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {artists.length === 0 && (
            <Box sx={{ textAlign: 'center', py: 4 }}>
              <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
              <Typography variant="h6" color="text.secondary">
                No artists found
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Create Artist Dialog */}
      <Dialog open={createDialogOpen} onClose={() => setCreateDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Create New Artist Account</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {/* User Account Section */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Account Details</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Username"
                value={newArtist.username}
                onChange={(e) => setNewArtist({ ...newArtist, username: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={newArtist.email}
                onChange={(e) => setNewArtist({ ...newArtist, email: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={newArtist.password}
                onChange={(e) => setNewArtist({ ...newArtist, password: e.target.value })}
                required
              />
            </Grid>

            {/* Artist Details Section */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Artist Information</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Artist Name"
                value={newArtist.artistName}
                onChange={(e) => setNewArtist({ ...newArtist, artistName: e.target.value })}
                required
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Stage Name"
                value={newArtist.stageName}
                onChange={(e) => setNewArtist({ ...newArtist, stageName: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Real Name"
                value={newArtist.realName}
                onChange={(e) => setNewArtist({ ...newArtist, realName: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                value={newArtist.phone}
                onChange={(e) => setNewArtist({ ...newArtist, phone: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Bio"
                multiline
                rows={3}
                value={newArtist.bio}
                onChange={(e) => setNewArtist({ ...newArtist, bio: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="City"
                value={newArtist.city}
                onChange={(e) => setNewArtist({ ...newArtist, city: e.target.value })}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website"
                value={newArtist.website}
                onChange={(e) => setNewArtist({ ...newArtist, website: e.target.value })}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCreateDialogOpen(false)}>Cancel</Button>
          <Button onClick={handleCreateArtist} variant="contained">Create Artist</Button>
        </DialogActions>
      </Dialog>

      {/* View Artist Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Artist Details</DialogTitle>
        <DialogContent>
          {selectedArtist && (
            <Box sx={{ mt: 2 }}>
              <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
                <Tab label="Profile" />
                <Tab label="Account" />
                <Tab label="Compliance" />
              </Tabs>

              {tabValue === 0 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Artist Name</Typography>
                      <Typography variant="body1">{selectedArtist.artistName}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Stage Name</Typography>
                      <Typography variant="body1">{selectedArtist.stageName || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12}>
                      <Typography variant="subtitle2" color="text.secondary">Bio</Typography>
                      <Typography variant="body1">{selectedArtist.bio || 'No bio available'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Genres</Typography>
                      <Typography variant="body1">{selectedArtist.genre?.join(', ') || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Location</Typography>
                      <Typography variant="body1">{selectedArtist.city}, {selectedArtist.country}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}

              {tabValue === 1 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Email</Typography>
                      <Typography variant="body1">{selectedArtist.email}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Phone</Typography>
                      <Typography variant="body1">{selectedArtist.phone || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Account Status</Typography>
                      <Chip
                        label={selectedArtist.accountStatus}
                        color={getStatusColor(selectedArtist.accountStatus)}
                        size="small"
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">Verification Status</Typography>
                      <Chip
                        icon={selectedArtist.verification?.isVerified ? <VerifiedIcon /> : <WarningIcon />}
                        label={selectedArtist.verification?.isVerified ? "Verified" : "Unverified"}
                        color={selectedArtist.verification?.isVerified ? "success" : "warning"}
                        size="small"
                      />
                    </Grid>
                  </Grid>
                </Box>
              )}

              {tabValue === 2 && (
                <Box sx={{ mt: 3 }}>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">IPI Number</Typography>
                      <Typography variant="body1">{selectedArtist.ipiNumber || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">SAMRO Member Number</Typography>
                      <Typography variant="body1">{selectedArtist.samroMemberNumber || 'N/A'}</Typography>
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" color="text.secondary">SAMPRA Member Number</Typography>
                      <Typography variant="body1">{selectedArtist.sampraMemberNumber || 'N/A'}</Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default MaterialArtistManagement;
