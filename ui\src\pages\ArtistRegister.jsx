import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Paper,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Avatar,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  useTheme,
  alpha,
  InputAdornment,
  IconButton,
  Stepper,
  Step,
  StepLabel
} from '@mui/material';
import {
  Person as PersonIcon,
  Visibility,
  VisibilityOff,
  MusicNote as MusicIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Language as WebsiteIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { registrationService } from '../services/api';

const ArtistRegister = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const navigate = useNavigate();
  const theme = useTheme();

  const [formData, setFormData] = useState({
    // Account details
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    // Artist details
    artistName: '',
    stageName: '',
    realName: '',
    phone: '',
    bio: '',
    genre: [],
    country: 'ZA',
    city: '',
    website: '',
    // Social media
    socialMedia: {
      instagram: '',
      twitter: '',
      facebook: '',
      youtube: ''
    },
    // Professional info
    manager: {
      name: '',
      email: '',
      phone: ''
    },
    agent: {
      name: '',
      email: '',
      phone: ''
    },
    // Compliance info
    ipiNumber: '',
    samroMemberNumber: '',
    sampraMemberNumber: ''
  });

  const steps = ['Account Details', 'Artist Information', 'Professional & Compliance'];

  const genres = [
    'Amapiano', 'House', 'Deep House', 'Afro House', 'Tech House',
    'Hip Hop', 'Rap', 'R&B', 'Afrobeats', 'Gqom', 'Kwaito',
    'Jazz', 'Gospel', 'Rock', 'Pop', 'Electronic', 'Other'
  ];

  const handleInputChange = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleGenreChange = (event) => {
    const value = event.target.value;
    setFormData(prev => ({
      ...prev,
      genre: typeof value === 'string' ? value.split(',') : value
    }));
  };

  const validateStep = (step) => {
    switch (step) {
      case 0:
        return formData.username && formData.email && formData.password && 
               formData.password === formData.confirmPassword;
      case 1:
        return formData.artistName && formData.genre.length > 0;
      case 2:
        return true; // Optional fields
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep(activeStep)) {
      setActiveStep(prev => prev + 1);
      setError('');
    } else {
      setError('Please fill in all required fields');
    }
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
    setError('');
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const registrationData = {
        // User account details
        username: formData.username,
        email: formData.email,
        password: formData.password,
        // Artist details
        artistName: formData.artistName,
        stageName: formData.stageName,
        realName: formData.realName,
        phone: formData.phone,
        bio: formData.bio,
        genre: formData.genre,
        country: formData.country,
        city: formData.city,
        website: formData.website,
        // Social media
        socialMedia: formData.socialMedia,
        // Professional info
        manager: formData.manager,
        agent: formData.agent,
        // Compliance info
        ipiNumber: formData.ipiNumber,
        samroMemberNumber: formData.samroMemberNumber,
        sampraMemberNumber: formData.sampraMemberNumber
      };

      const response = await registrationService.registerArtist(registrationData);
      setSuccess('Registration successful! Please check your email for verification instructions.');
      
      // Redirect to verification page after 3 seconds
      setTimeout(() => {
        navigate('/verify', { 
          state: { 
            email: formData.email, 
            type: 'artist',
            verificationId: response.data.verificationId 
          } 
        });
      }, 3000);

    } catch (error) {
      console.error('Registration error:', error);
      setError(error.response?.data?.error || 'Registration failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Account Details</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Username"
                value={formData.username}
                onChange={(e) => handleInputChange('username', e.target.value)}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PersonIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                required
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <EmailIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Password"
                type={showPassword ? 'text' : 'password'}
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                required
                InputProps={{
                  endAdornment: (
                    <InputAdornment position="end">
                      <IconButton
                        onClick={() => setShowPassword(!showPassword)}
                        edge="end"
                      >
                        {showPassword ? <VisibilityOff /> : <Visibility />}
                      </IconButton>
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                required
                error={formData.confirmPassword && formData.password !== formData.confirmPassword}
                helperText={formData.confirmPassword && formData.password !== formData.confirmPassword ? 'Passwords do not match' : ''}
              />
            </Grid>
          </Grid>
        );

      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Artist Information</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Artist Name"
                value={formData.artistName}
                onChange={(e) => handleInputChange('artistName', e.target.value)}
                required
                helperText="Your official artist name"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Stage Name"
                value={formData.stageName}
                onChange={(e) => handleInputChange('stageName', e.target.value)}
                helperText="Performance name (if different)"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Real Name"
                value={formData.realName}
                onChange={(e) => handleInputChange('realName', e.target.value)}
                helperText="Your legal name"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleInputChange('phone', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <PhoneIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth required>
                <InputLabel>Genres</InputLabel>
                <Select
                  multiple
                  value={formData.genre}
                  onChange={handleGenreChange}
                  label="Genres"
                  renderValue={(selected) => (
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                      {selected.map((value) => (
                        <Chip key={value} label={value} size="small" />
                      ))}
                    </Box>
                  )}
                >
                  {genres.map((genre) => (
                    <MenuItem key={genre} value={genre}>
                      {genre}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Bio"
                multiline
                rows={4}
                value={formData.bio}
                onChange={(e) => handleInputChange('bio', e.target.value)}
                helperText="Tell us about your music and career"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="City"
                value={formData.city}
                onChange={(e) => handleInputChange('city', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Website"
                value={formData.website}
                onChange={(e) => handleInputChange('website', e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <WebsiteIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
          </Grid>
        );

      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>Professional & Compliance Information</Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                All fields in this section are optional but recommended for professional artists.
              </Typography>
            </Grid>
            
            {/* Social Media */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>Social Media</Typography>
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Instagram"
                value={formData.socialMedia.instagram}
                onChange={(e) => handleInputChange('socialMedia.instagram', e.target.value)}
                placeholder="@username"
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label="Twitter"
                value={formData.socialMedia.twitter}
                onChange={(e) => handleInputChange('socialMedia.twitter', e.target.value)}
                placeholder="@username"
              />
            </Grid>
            
            {/* Compliance Information */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom sx={{ mt: 2 }}>Compliance Information</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="IPI Number"
                value={formData.ipiNumber}
                onChange={(e) => handleInputChange('ipiNumber', e.target.value)}
                helperText="International Performer Identifier"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="SAMRO Member Number"
                value={formData.samroMemberNumber}
                onChange={(e) => handleInputChange('samroMemberNumber', e.target.value)}
                helperText="South African Music Rights Organisation"
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                label="SAMPRA Member Number"
                value={formData.sampraMemberNumber}
                onChange={(e) => handleInputChange('sampraMemberNumber', e.target.value)}
                helperText="South African Music Performance Rights Association"
              />
            </Grid>
          </Grid>
        );

      default:
        return null;
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        py: 4
      }}
    >
      <Container maxWidth="md">
        <Paper
          elevation={24}
          sx={{
            p: 4,
            borderRadius: 3,
            background: 'rgba(255, 255, 255, 0.95)',
            backdropFilter: 'blur(10px)',
          }}
        >
          {/* Header */}
          <Box sx={{ textAlign: 'center', mb: 4 }}>
            <Avatar
              sx={{
                width: 80,
                height: 80,
                bgcolor: 'secondary.main',
                mx: 'auto',
                mb: 2
              }}
            >
              <PersonIcon sx={{ fontSize: 40 }} />
            </Avatar>
            <Typography variant="h4" component="h1" gutterBottom sx={{ fontWeight: 'bold' }}>
              Artist Registration
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Join TrakSong as an artist and manage your music catalogue
            </Typography>
          </Box>

          {/* Back to Login */}
          <Box sx={{ mb: 3 }}>
            <Button
              component={Link}
              to="/login"
              startIcon={<ArrowBackIcon />}
              sx={{ textTransform: 'none' }}
            >
              Back to Login
            </Button>
          </Box>

          {/* Stepper */}
          <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>

          {/* Alerts */}
          {error && (
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          )}

          {success && (
            <Alert severity="success" sx={{ mb: 3 }}>
              {success}
            </Alert>
          )}

          {/* Form */}
          <Box component="form" onSubmit={handleSubmit}>
            {renderStepContent(activeStep)}

            {/* Navigation Buttons */}
            <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
              <Button
                onClick={handleBack}
                disabled={activeStep === 0}
                sx={{ textTransform: 'none' }}
              >
                Back
              </Button>

              {activeStep === steps.length - 1 ? (
                <Button
                  type="submit"
                  variant="contained"
                  disabled={loading}
                  sx={{
                    minWidth: 120,
                    textTransform: 'none',
                    fontWeight: 600
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Register'}
                </Button>
              ) : (
                <Button
                  onClick={handleNext}
                  variant="contained"
                  disabled={!validateStep(activeStep)}
                  sx={{
                    textTransform: 'none',
                    fontWeight: 600
                  }}
                >
                  Next
                </Button>
              )}
            </Box>
          </Box>

          {/* Login Link */}
          <Box sx={{ textAlign: 'center', mt: 3 }}>
            <Typography variant="body2" color="text.secondary">
              Already have an account?{' '}
              <Button component={Link} to="/login" sx={{ textTransform: 'none' }}>
                Sign In
              </Button>
            </Typography>
          </Box>
        </Paper>
      </Container>
    </Box>
  );
};

export default ArtistRegister;
